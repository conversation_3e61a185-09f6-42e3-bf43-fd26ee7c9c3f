# Worker服务 - 后台任务处理服务

Worker服务是Deep Risk RAG系统的后台任务处理引擎，负责文件向量化、风险分析等计算密集型任务，支持完全独立部署和水平扩展。

## ✨ 特性

- 🚀 **完全独立部署** - 可脱离主项目独立运行
- ⚡ **异步任务处理** - 基于Celery的高性能任务队列
- 📈 **水平扩展** - 支持动态扩展和负载均衡
- 🔄 **故障恢复** - 自动重试和错误处理机制
- 📊 **实时监控** - Flower监控界面和指标收集
- 🎯 **任务调度** - Celery Beat定时任务支持
- 🛡️ **健康检查** - 完整的健康监控和自愈能力
- 📦 **Docker支持** - 提供完整的容器化部署方案
- 🔒 **生产就绪** - 包含安全、监控和日志配置

## 🏗️ 架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Worker Service Cluster                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Worker    │  │   Worker    │  │   Worker    │         │
│  │      1      │  │      2      │  │      N      │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐                          │
│  │   Flower    │  │ Celery Beat │                          │
│  │ (Monitor)   │  │ (Scheduler) │                          │
│  └─────────────┘  └─────────────┘                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      Task Queue (Redis)                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │Vectorization│  │  Analysis   │  │   Health    │         │
│  │    Queue    │  │    Queue    │  │    Queue    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    External Services                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  ChromaDB   │  │  Embedding  │  │     LLM     │         │
│  │  (Vector)   │  │   Service   │  │   Provider  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 方式1：直接启动

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置环境
cp .env.example .env
# 编辑 .env 文件，设置必要的环境变量

# 3. 启动Worker
./start.sh --worker

# 或者启动所有服务
./start.sh --all
```

### 方式2：使用启动脚本

```bash
# 启动Celery Worker
./start.sh --worker

# 启动Flower监控
./start.sh --flower

# 启动Celery Beat调度器
./start.sh --beat

# 启动所有服务
./start.sh --all

# 检查环境和依赖
./start.sh --check

# 安装依赖
./start.sh --install

# 运行测试
./start.sh --test
```

### 方式3：Docker部署

```bash
# 基本启动 (Worker + Redis)
docker-compose up -d

# 包含监控
docker-compose --profile monitoring up -d

# 包含调度器
docker-compose --profile scheduler up -d

# 多Worker实例
docker-compose up -d --scale worker=3

# 完整服务栈
docker-compose --profile monitoring --profile scheduler --profile with-chromadb --profile with-embedding up -d
```

## 📋 任务类型

### 向量化任务
- **文件向量化**: 将CSV/Excel文件转换为向量存储
- **文档分块**: 智能文档分割和预处理
- **嵌入计算**: 调用BGE-M3嵌入服务生成向量

### 分析任务
- **风险分析**: 基于LLM的风险评估和预测
- **批量分析**: 多文件并行分析处理
- **结果聚合**: 分析结果的整合和格式化

### 系统任务
- **健康检查**: 定期系统健康状态检查
- **缓存清理**: 自动清理过期缓存和临时文件
- **指标收集**: 性能指标和统计数据收集

## ⚙️ 配置

### 环境变量

主要环境变量配置：

```bash
# 服务配置
LOG_LEVEL=INFO
WORKER_CONCURRENCY=4

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 外部服务
CHROMA_URL=http://localhost:8001
EMBEDDING_SERVICE_URL=http://localhost:8004

# LLM配置
LLM_PROVIDER=deepseek
DEEPSEEK_API_KEY=your_api_key_here

# 扩展配置
ENABLE_AUTO_SCALING=false
MIN_WORKERS=1
MAX_WORKERS=10
```

完整配置请参考 `.env.example` 文件。

### 水平扩展配置

Worker服务支持自动水平扩展：

```bash
# 启用自动扩展
ENABLE_AUTO_SCALING=true

# 扩展阈值
SCALE_UP_THRESHOLD=85.0    # CPU/内存使用率超过85%时扩容
SCALE_DOWN_THRESHOLD=30.0  # CPU/内存使用率低于30%时缩容

# Worker数量限制
MIN_WORKERS=1
MAX_WORKERS=10

# 扩展冷却时间
SCALE_COOLDOWN_SECONDS=300
```

## 🔧 开发

### 项目结构

```
services/worker_service/
├── core/                  # 核心组件
│   ├── analyzer.py        # 风险分析器
│   ├── embedding_client.py # 嵌入服务客户端
│   ├── embeddings.py      # 嵌入处理
│   ├── llm_client.py      # LLM客户端
│   ├── scaling.py         # 水平扩展支持
│   └── vectorizer.py      # 向量化处理
├── tasks/                 # 任务定义
│   ├── analysis.py        # 分析任务
│   ├── health.py          # 健康检查任务
│   └── vectorization.py   # 向量化任务
├── config.py              # 配置管理
├── worker.py              # Celery应用
├── requirements.txt       # 依赖列表
├── start.sh               # 启动脚本
├── healthcheck.sh         # 健康检查脚本
├── Dockerfile             # Docker配置
├── docker-compose.yml     # Docker Compose配置
└── README.md              # 文档
```

### 添加新任务

1. 在 `tasks/` 目录下创建任务文件
2. 定义任务函数并使用 `@app.task` 装饰器
3. 在 `worker.py` 中导入任务
4. 更新任务文档

示例：

```python
from worker import app

@app.task(bind=True, max_retries=3)
def my_custom_task(self, data):
    try:
        # 任务逻辑
        result = process_data(data)
        return result
    except Exception as exc:
        # 错误处理和重试
        raise self.retry(exc=exc, countdown=60)
```

### 监控和调试

```bash
# 查看Worker状态
celery -A worker inspect active

# 查看队列状态
celery -A worker inspect reserved

# 查看统计信息
celery -A worker inspect stats

# 清空队列
celery -A worker purge

# 重启Worker
celery -A worker control shutdown
```

## 📊 监控

### Flower监控界面

访问 `http://localhost:5555` 查看：

- Worker状态和性能
- 任务执行情况
- 队列统计信息
- 实时任务监控

### 健康检查

Worker服务提供多种健康检查：

```bash
# 检查Worker进程
./healthcheck.sh

# 检查任务执行能力
celery -A worker inspect ping

# 检查队列连接
redis-cli -u $REDIS_URL ping
```

### 日志监控

```bash
# 查看Worker日志
tail -f logs/worker_service.log

# Docker环境查看日志
docker-compose logs -f worker

# 查看特定任务日志
grep "task_id" logs/worker_service.log
```

## 🚀 部署

### 生产部署建议

1. **资源配置**
   - 根据任务类型调整Worker并发数
   - 配置适当的内存和CPU限制
   - 启用自动扩展功能

2. **高可用配置**
   - 部署多个Worker实例
   - 配置Redis集群
   - 启用任务重试和死信队列

3. **监控配置**
   - 启用Prometheus指标收集
   - 配置Grafana仪表板
   - 设置告警规则

4. **安全配置**
   - 启用任务签名验证
   - 配置网络访问控制
   - 使用安全的Redis连接

### 性能优化

1. **任务优化**
   - 合理设置任务超时时间
   - 使用批处理减少网络开销
   - 启用结果缓存

2. **资源优化**
   - 调整Worker预取数量
   - 配置内存限制
   - 使用连接池

3. **扩展优化**
   - 根据负载动态调整Worker数量
   - 使用任务路由分离不同类型任务
   - 配置优先级队列

## 🧪 测试

```bash
# 运行所有测试
./start.sh --test

# 或者直接使用pytest
pytest tests/ -v

# 运行特定测试
pytest tests/test_tasks.py -v

# 生成覆盖率报告
pytest --cov=services.worker_service tests/

# 测试任务执行
python -c "
from tasks.health import health_check
result = health_check.delay()
print(result.get())
"
```

## 🤝 贡献

1. Fork项目
2. 创建功能分支
3. 提交变更
4. 创建Pull Request

## 📄 许可证

MIT License
